/**
 * @note
 * filter
 *
 * <AUTHOR>
 * @date 	2025-09-03
 */
package filter

import (
	"context"
	"gitlab.docsl.com/security/common"
	"gitlab.docsl.com/security/common/mysql"
	socCommon "gitlab.docsl.com/security/socv2/soc/pkg/common"
	"gorm.io/gorm"
)

type FilterRuleModel interface {
	// FilterRule methods
	GetDB(ctx context.Context) (*gorm.DB, error)
	CreateFilterRule(ctx context.Context, tx *gorm.DB, rule *RuleTable) (int64, error)
	DeleteFilterRule(ctx context.Context, tx *gorm.DB, ruleID string) error
	QueryLatestFilterRuleByRuleID(ctx context.Context, tx *gorm.DB, ruleID string, status []socCommon.FilterRuleStatus) (*RuleTable, error)
	UpdateFilterRuleByID(ctx context.Context, tx *gorm.DB, id uint, businessID *string, tenantID *TenantIDArray, name *string, desc *string, ruleType *socCommon.FilterRuleType, filterConfig *FilterRuleConfig, dedupConfig *DeduplicationRuleConfig, status *socCommon.FilterRuleStatus, version *int64) error
	QueryFilterRuleBySeveralConditions(ctx context.Context, tx *gorm.DB, filter FilterRuleQueryFilter) ([]*RuleTable, error)
	QueryFilterRuleCountBySeveralConditions(ctx context.Context, tx *gorm.DB, filter FilterRuleQueryFilter) (int64, error)
	QueryLatestFilterRuleBySeveralConditions(ctx context.Context, tx *gorm.DB, filter FilterRuleQueryFilter) ([]*RuleTable, error)
	QueryLatestFilterRuleCountBySeveralConditions(ctx context.Context, tx *gorm.DB, filter FilterRuleQueryFilter) (int64, error)
}

type FilterRuleModelImpl struct{}

var DefaultService FilterRuleModel = &FilterRuleModelImpl{}

func (m *FilterRuleModelImpl) GetDB(ctx context.Context) (db *gorm.DB, err error) {
	return mysql.GetDB(socCommon.DBName, false, common.GetLogger(ctx))
}

func GetDB(ctx context.Context) (*gorm.DB, error) {
	return DefaultService.GetDB(ctx)
}

// Public functions for FilterRule
func CreateFilterRule(ctx context.Context, rule *RuleTable) (int64, error) {
	db, err := DefaultService.GetDB(ctx)
	if err != nil {
		return 0, err
	}
	return DefaultService.CreateFilterRule(ctx, db, rule)
}

func CreateFilterRuleWithTx(ctx context.Context, tx *gorm.DB, rule *RuleTable) (int64, error) {
	return DefaultService.CreateFilterRule(ctx, tx, rule)
}

func QueryLatestFilterRuleByRuleID(ctx context.Context, tx *gorm.DB, ruleID string, status []socCommon.FilterRuleStatus) (*RuleTable, error) {
	return DefaultService.QueryLatestFilterRuleByRuleID(ctx, tx, ruleID, status)
}

func DeleteFilterRule(ctx context.Context, ruleID string) error {
	db, err := DefaultService.GetDB(ctx)
	if err != nil {
		return err
	}
	return DefaultService.DeleteFilterRule(ctx, db, ruleID)
}

func DeleteFilterRuleWithTx(ctx context.Context, tx *gorm.DB, ruleID string) error {
	return DefaultService.DeleteFilterRule(ctx, tx, ruleID)
}

func UpdateFilterRuleByID(ctx context.Context, id uint, businessID *string, tenantID *TenantIDArray, name *string, desc *string, ruleType *socCommon.FilterRuleType, filterConfig *FilterRuleConfig, dedupConfig *DeduplicationRuleConfig, status *socCommon.FilterRuleStatus, version *int64) error {
	db, err := DefaultService.GetDB(ctx)
	if err != nil {
		return err
	}
	return DefaultService.UpdateFilterRuleByID(ctx, db, id, businessID, tenantID, name, desc, ruleType, filterConfig, dedupConfig, status, version)
}

func UpdateFilterRuleByIDWithTx(ctx context.Context, tx *gorm.DB, id uint, businessID *string, tenantID *TenantIDArray, name *string, desc *string, ruleType *socCommon.FilterRuleType, filterConfig *FilterRuleConfig, dedupConfig *DeduplicationRuleConfig, status *socCommon.FilterRuleStatus, version *int64) error {
	return DefaultService.UpdateFilterRuleByID(ctx, tx, id, businessID, tenantID, name, desc, ruleType, filterConfig, dedupConfig, status, version)
}

func QueryFilterRuleBySeveralConditions(ctx context.Context, filter FilterRuleQueryFilter) ([]*RuleTable, error) {
	db, err := DefaultService.GetDB(ctx)
	if err != nil {
		return nil, err
	}
	return DefaultService.QueryFilterRuleBySeveralConditions(ctx, db, filter)
}

func QueryFilterRuleBySeveralConditionsWithTx(ctx context.Context, tx *gorm.DB, filter FilterRuleQueryFilter) ([]*RuleTable, error) {
	return DefaultService.QueryFilterRuleBySeveralConditions(ctx, tx, filter)
}

func QueryFilterRuleCountBySeveralConditions(ctx context.Context, filter FilterRuleQueryFilter) (int64, error) {
	db, err := DefaultService.GetDB(ctx)
	if err != nil {
		return 0, err
	}
	return DefaultService.QueryFilterRuleCountBySeveralConditions(ctx, db, filter)
}

func QueryFilterRuleCountBySeveralConditionsWithTx(ctx context.Context, tx *gorm.DB, filter FilterRuleQueryFilter) (int64, error) {
	return DefaultService.QueryFilterRuleCountBySeveralConditions(ctx, tx, filter)
}

func QueryLatestFilterRule(ctx context.Context, filter FilterRuleQueryFilter) ([]*RuleTable, error) {
	db, err := DefaultService.GetDB(ctx)
	if err != nil {
		return nil, err
	}
	return DefaultService.QueryLatestFilterRuleBySeveralConditions(ctx, db, filter)
}

func QueryLatestFilterRuleWithTx(ctx context.Context, tx *gorm.DB, filter FilterRuleQueryFilter) ([]*RuleTable, error) {
	return DefaultService.QueryLatestFilterRuleBySeveralConditions(ctx, tx, filter)
}

func QueryLatestFilterRuleCount(ctx context.Context, filter FilterRuleQueryFilter) (int64, error) {
	db, err := DefaultService.GetDB(ctx)
	if err != nil {
		return 0, err
	}
	return DefaultService.QueryLatestFilterRuleCountBySeveralConditions(ctx, db, filter)
}

func QueryLatestFilterRuleCountWithTx(ctx context.Context, tx *gorm.DB, filter FilterRuleQueryFilter) (int64, error) {
	return DefaultService.QueryLatestFilterRuleCountBySeveralConditions(ctx, tx, filter)
}
